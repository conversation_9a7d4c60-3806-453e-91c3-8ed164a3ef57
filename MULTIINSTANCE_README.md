# Multiinstance Support for Publish Application

The Publish 3 application now supports running multiple instances simultaneously. This feature allows users to work on multiple projects concurrently without conflicts.

## Features

### Instance Management
- **Unique Instance Identification**: Each instance gets a unique UUID for identification
- **Instance Registry**: Centralized registry tracks all running instances
- **Heartbeat Monitoring**: Automatic detection of dead/crashed instances
- **Instance Numbering**: Instances are numbered based on launch order (1, 2, 3, etc.)

### Resource Isolation
- **Separate Log Files**: Each instance writes to its own log file
- **Independent Service Containers**: Each instance has its own service container
- **Isolated Configuration**: Instance-specific configuration handling
- **Process Tracking**: Proper cleanup and resource management per instance

### User Interface
- **Instance Information Display**: Shows current instance number and total count
- **Instance List Viewer**: View all running instances with details
- **Window Title Identification**: Window titles include instance information
- **Visual Instance Indicators**: Clear identification of which instance you're using

## Usage

### Basic Usage

#### Launch Single Instance
```bash
python apps/publish_3.py
```

#### Launch with Custom Instance ID
```bash
python apps/publish_3.py --instance-id my_custom_id
```

#### Force New Instance
```bash
python apps/publish_3.py --new-instance
```

### Multiple Instances

#### Using the Launcher Script
```bash
# Launch 3 instances (default)
python launch_multiple_instances.py

# Launch 5 instances
python launch_multiple_instances.py --count 5

# Launch with delay between instances
python launch_multiple_instances.py --count 3 --delay 2.0
```

#### Manual Launch
Open multiple terminal windows and run:
```bash
# Terminal 1
python apps/publish_3.py --instance-id project_a

# Terminal 2  
python apps/publish_3.py --instance-id project_b

# Terminal 3
python apps/publish_3.py --instance-id project_c
```

## Instance Information

### GUI Display
When multiple instances are running, each window shows:
- Instance number (e.g., "Instance #2 of 3 running instances")
- Shortened instance ID for instances beyond the first
- "Show All Instances" button to view details of all running instances

### Instance Details
Each instance tracks:
- **Instance ID**: Unique identifier (UUID)
- **Process ID**: Operating system process ID
- **Start Time**: When the instance was launched
- **Window Title**: Current window title
- **Project Path**: Path to currently loaded project (if any)
- **Status**: Running, dead, or timeout
- **Last Heartbeat**: Last activity timestamp

## Technical Implementation

### Instance Manager
The `InstanceManager` class handles:
- Instance registration and discovery
- Heartbeat monitoring
- Dead instance cleanup
- Inter-instance communication

### Service Container Updates
- Instance-aware service creation
- Isolated resource management
- Instance-specific logging configuration

### Logging Enhancements
- Instance ID included in log file names
- Instance information in log messages
- Separate log files prevent conflicts

## File Structure

```
apps/publish/utils/instance_manager.py    # Core instance management
apps/publish_3.py                         # Updated main application
apps/publish/container.py                 # Updated service container
apps/publish/config/logging_config.py     # Updated logging configuration
apps/publish/gui/main_window.py           # Updated GUI with instance info
test_multiinstance.py                     # Test script
launch_multiple_instances.py              # Launcher utility
```

## Testing

### Automated Testing
```bash
# Test instance manager only
python test_multiinstance.py --manager-only

# Full multiinstance test (launches GUI instances)
python test_multiinstance.py --instances 3

# Custom test configuration
python test_multiinstance.py --instances 5 --delay 1.5
```

### Manual Testing
1. Launch multiple instances using the launcher script
2. Verify each instance shows correct instance information
3. Test "Show All Instances" functionality
4. Verify instances can work independently
5. Test graceful shutdown of individual instances

## Configuration

### Instance Registry
- Location: `%TEMP%/publish_app_instances/instances.json`
- Contains: Instance metadata and status information
- Cleanup: Automatic removal of dead instances

### Log Files
- Pattern: `publish_3_{instance_id_short}.log`
- Location: Application directory or user temp directory
- Rotation: Standard log rotation applies per instance

## Troubleshooting

### Common Issues

#### Instance Not Detected
- Check if instance registry file exists and is readable
- Verify process permissions
- Look for errors in instance-specific log files

#### Multiple Instances Show Same Number
- Wait a few seconds for instance registration to complete
- Check if heartbeat thread is running properly
- Verify instance manager initialization

#### Resource Conflicts
- Each instance should have isolated resources
- Check log files for COM object conflicts
- Verify service container isolation

### Debug Information
Enable debug logging to see detailed instance management:
```python
import logging
logging.getLogger('apps.publish.utils.instance_manager').setLevel(logging.DEBUG)
```

## Best Practices

### Performance
- Limit concurrent instances based on system resources
- Monitor memory usage with multiple instances
- Consider E3 Series connection limits

### Workflow
- Use descriptive instance IDs for project identification
- Close unused instances to free resources
- Use instance information to track which project you're working on

### Development
- Test multiinstance scenarios during development
- Ensure proper resource cleanup in all code paths
- Use instance-aware logging for debugging

## Future Enhancements

Potential improvements for multiinstance support:
- Inter-instance communication for project sharing
- Instance-specific configuration profiles
- Advanced instance management GUI
- Project-based instance grouping
- Automatic instance load balancing

## Support

For issues related to multiinstance functionality:
1. Check the instance-specific log files
2. Run the test script to verify functionality
3. Review the instance registry for conflicts
4. Ensure proper cleanup of dead instances
