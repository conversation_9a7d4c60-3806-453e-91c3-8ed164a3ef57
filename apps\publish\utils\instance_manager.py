"""
Instance Manager for Publish Application.

This module provides functionality to manage multiple instances of the publish
application, including instance detection, unique identification, and coordination
between instances.

Features:
- Unique instance identification using UUIDs
- Instance registration and discovery
- Process-based instance detection
- Instance-specific resource management
- Inter-instance communication capabilities
- Graceful instance cleanup

Author: E3 Automation Team
"""

import os
import sys
import json
import uuid
import time
import psutil
import logging
import tempfile
import threading
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta


@dataclass
class InstanceInfo:
    """Information about a publish application instance."""
    
    instance_id: str
    process_id: int
    start_time: float
    window_title: str = ""
    project_path: str = ""
    status: str = "running"
    last_heartbeat: float = 0.0
    
    def __post_init__(self):
        """Initialize default values."""
        if not self.last_heartbeat:
            self.last_heartbeat = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InstanceInfo':
        """Create from dictionary."""
        return cls(**data)
    
    def is_alive(self, timeout: float = 30.0) -> bool:
        """
        Check if instance is still alive based on heartbeat.
        
        Args:
            timeout: Timeout in seconds for considering instance dead
            
        Returns:
            True if instance is considered alive
        """
        return (time.time() - self.last_heartbeat) < timeout
    
    def update_heartbeat(self):
        """Update the heartbeat timestamp."""
        self.last_heartbeat = time.time()


class InstanceManager:
    """
    Manages multiple instances of the publish application.
    
    This class handles instance registration, discovery, and coordination
    between multiple running instances of the publish application.
    """
    
    def __init__(self, app_name: str = "publish_app"):
        """
        Initialize the instance manager.
        
        Args:
            app_name: Name of the application for instance tracking
        """
        self.app_name = app_name
        self.logger = logging.getLogger(__name__)
        
        # Generate unique instance ID
        self.instance_id = str(uuid.uuid4())
        self.process_id = os.getpid()
        self.start_time = time.time()
        
        # Instance registry file path
        self.registry_dir = Path(tempfile.gettempdir()) / f"{app_name}_instances"
        self.registry_dir.mkdir(exist_ok=True)
        self.registry_file = self.registry_dir / "instances.json"
        
        # Instance-specific data
        self.instance_info = InstanceInfo(
            instance_id=self.instance_id,
            process_id=self.process_id,
            start_time=self.start_time
        )
        
        # Heartbeat thread
        self._heartbeat_thread = None
        self._heartbeat_stop_event = threading.Event()
        self._heartbeat_interval = 10.0  # seconds
        
        # Lock for thread safety
        self._lock = threading.Lock()
        
        self.logger.info(f"Instance manager initialized: {self.instance_id}")
    
    def register_instance(self, window_title: str = "", project_path: str = "") -> bool:
        """
        Register this instance in the global registry.
        
        Args:
            window_title: Title of the application window
            project_path: Path to the current project
            
        Returns:
            True if registration successful
        """
        try:
            with self._lock:
                # Update instance info
                self.instance_info.window_title = window_title
                self.instance_info.project_path = project_path
                self.instance_info.update_heartbeat()
                
                # Load existing registry
                instances = self._load_registry()
                
                # Add or update this instance
                instances[self.instance_id] = self.instance_info.to_dict()
                
                # Save registry
                self._save_registry(instances)
                
                # Start heartbeat thread if not already running
                if not self._heartbeat_thread or not self._heartbeat_thread.is_alive():
                    self._start_heartbeat()
                
                self.logger.info(f"Instance registered: {self.instance_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to register instance: {e}")
            return False
    
    def unregister_instance(self) -> bool:
        """
        Unregister this instance from the global registry.
        
        Returns:
            True if unregistration successful
        """
        try:
            with self._lock:
                # Stop heartbeat
                self._stop_heartbeat()
                
                # Load existing registry
                instances = self._load_registry()
                
                # Remove this instance
                instances.pop(self.instance_id, None)
                
                # Save registry
                self._save_registry(instances)
                
                self.logger.info(f"Instance unregistered: {self.instance_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to unregister instance: {e}")
            return False
    
    def get_running_instances(self, include_dead: bool = False) -> List[InstanceInfo]:
        """
        Get list of all running instances.
        
        Args:
            include_dead: Whether to include instances that appear dead
            
        Returns:
            List of InstanceInfo objects
        """
        try:
            instances = self._load_registry()
            result = []
            
            for instance_data in instances.values():
                instance_info = InstanceInfo.from_dict(instance_data)
                
                # Check if process is still running
                try:
                    process = psutil.Process(instance_info.process_id)
                    if not process.is_running():
                        if not include_dead:
                            continue
                        instance_info.status = "dead"
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    if not include_dead:
                        continue
                    instance_info.status = "dead"
                
                # Check heartbeat
                if not instance_info.is_alive():
                    if not include_dead:
                        continue
                    instance_info.status = "timeout"
                
                result.append(instance_info)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to get running instances: {e}")
            return []
    
    def cleanup_dead_instances(self) -> int:
        """
        Remove dead instances from the registry.
        
        Returns:
            Number of instances cleaned up
        """
        try:
            with self._lock:
                instances = self._load_registry()
                initial_count = len(instances)
                
                # Filter out dead instances
                alive_instances = {}
                for instance_id, instance_data in instances.items():
                    instance_info = InstanceInfo.from_dict(instance_data)
                    
                    # Check if process is still running and heartbeat is recent
                    is_alive = False
                    try:
                        process = psutil.Process(instance_info.process_id)
                        if process.is_running() and instance_info.is_alive():
                            is_alive = True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                    
                    if is_alive:
                        alive_instances[instance_id] = instance_data
                
                # Save cleaned registry
                self._save_registry(alive_instances)
                
                cleaned_count = initial_count - len(alive_instances)
                if cleaned_count > 0:
                    self.logger.info(f"Cleaned up {cleaned_count} dead instances")
                
                return cleaned_count
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup dead instances: {e}")
            return 0
    
    def get_instance_count(self) -> int:
        """
        Get the number of running instances.
        
        Returns:
            Number of running instances
        """
        return len(self.get_running_instances())
    
    def is_first_instance(self) -> bool:
        """
        Check if this is the first (primary) instance.
        
        Returns:
            True if this is the first instance
        """
        instances = self.get_running_instances()
        if not instances:
            return True
        
        # Sort by start time to find the first instance
        instances.sort(key=lambda x: x.start_time)
        return instances[0].instance_id == self.instance_id
    
    def get_instance_number(self) -> int:
        """
        Get the instance number (1-based) based on start order.
        
        Returns:
            Instance number (1 for first instance, 2 for second, etc.)
        """
        instances = self.get_running_instances()
        instances.sort(key=lambda x: x.start_time)
        
        for i, instance in enumerate(instances, 1):
            if instance.instance_id == self.instance_id:
                return i
        
        return 1  # Default to 1 if not found
    
    def update_project_info(self, window_title: str = "", project_path: str = ""):
        """
        Update the project information for this instance.
        
        Args:
            window_title: Updated window title
            project_path: Updated project path
        """
        try:
            with self._lock:
                # Update instance info
                if window_title:
                    self.instance_info.window_title = window_title
                if project_path:
                    self.instance_info.project_path = project_path
                
                self.instance_info.update_heartbeat()
                
                # Update registry
                instances = self._load_registry()
                instances[self.instance_id] = self.instance_info.to_dict()
                self._save_registry(instances)
                
        except Exception as e:
            self.logger.error(f"Failed to update project info: {e}")
    
    def _load_registry(self) -> Dict[str, Dict[str, Any]]:
        """Load the instance registry from file."""
        try:
            if self.registry_file.exists():
                with open(self.registry_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load instance registry: {e}")
        
        return {}
    
    def _save_registry(self, instances: Dict[str, Dict[str, Any]]):
        """Save the instance registry to file."""
        try:
            with open(self.registry_file, 'w') as f:
                json.dump(instances, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save instance registry: {e}")
    
    def _start_heartbeat(self):
        """Start the heartbeat thread."""
        if self._heartbeat_thread and self._heartbeat_thread.is_alive():
            return
        
        self._heartbeat_stop_event.clear()
        self._heartbeat_thread = threading.Thread(target=self._heartbeat_worker, daemon=True)
        self._heartbeat_thread.start()
        self.logger.debug("Heartbeat thread started")
    
    def _stop_heartbeat(self):
        """Stop the heartbeat thread."""
        if self._heartbeat_thread and self._heartbeat_thread.is_alive():
            self._heartbeat_stop_event.set()
            self._heartbeat_thread.join(timeout=5.0)
            self.logger.debug("Heartbeat thread stopped")
    
    def _heartbeat_worker(self):
        """Heartbeat worker thread."""
        while not self._heartbeat_stop_event.wait(self._heartbeat_interval):
            try:
                with self._lock:
                    self.instance_info.update_heartbeat()
                    instances = self._load_registry()
                    if self.instance_id in instances:
                        instances[self.instance_id] = self.instance_info.to_dict()
                        self._save_registry(instances)
            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.unregister_instance()
