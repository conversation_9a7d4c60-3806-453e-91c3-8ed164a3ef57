#!/usr/bin/env python3
"""
Simple launcher script for multiple publish application instances.

This script provides an easy way to launch multiple instances of the
publish application for testing multiinstance functionality.

Usage:
    python launch_multiple_instances.py [--count N]

Author: E3 Automation Team
"""

import os
import sys
import time
import subprocess
import argparse
import logging
from pathlib import Path


def launch_instances(count: int = 3, delay: float = 1.0):
    """
    Launch multiple instances of the publish application.
    
    Args:
        count: Number of instances to launch
        delay: Delay between launches in seconds
    """
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s"
    )
    logger = logging.getLogger(__name__)
    
    # Path to the publish application
    script_dir = Path(__file__).parent
    publish_app_path = script_dir / "apps" / "publish_3.py"
    
    if not publish_app_path.exists():
        logger.error(f"Publish application not found: {publish_app_path}")
        return
    
    logger.info(f"Launching {count} instances of the publish application...")
    
    processes = []
    
    try:
        for i in range(count):
            logger.info(f"Launching instance {i+1}...")
            
            # Create unique instance ID
            instance_id = f"manual_instance_{i+1}_{int(time.time())}"
            
            # Launch command
            cmd = [
                sys.executable,
                str(publish_app_path),
                "--instance-id", instance_id
            ]
            
            # Launch process
            process = subprocess.Popen(cmd)
            processes.append(process)
            
            logger.info(f"Launched instance {i+1} with PID: {process.pid}")
            
            # Wait between launches
            if i < count - 1:
                time.sleep(delay)
        
        logger.info(f"All {count} instances launched successfully!")
        logger.info("Each instance will have its own window and instance information.")
        logger.info("You can close instances individually or press Ctrl+C to terminate all.")
        
        # Wait for user to terminate
        try:
            while True:
                # Check if any processes have terminated
                running_count = sum(1 for p in processes if p.poll() is None)
                if running_count == 0:
                    logger.info("All instances have terminated.")
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Terminating all instances...")
            
            # Terminate all processes
            for i, process in enumerate(processes):
                if process.poll() is None:
                    logger.info(f"Terminating instance {i+1} (PID: {process.pid})")
                    process.terminate()
            
            # Wait for graceful termination
            for i, process in enumerate(processes):
                try:
                    process.wait(timeout=5)
                    logger.info(f"Instance {i+1} terminated gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Instance {i+1} did not terminate gracefully, killing...")
                    process.kill()
                    process.wait()
            
            logger.info("All instances terminated.")
    
    except Exception as e:
        logger.error(f"Error launching instances: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Launch multiple publish application instances")
    parser.add_argument("--count", type=int, default=3, help="Number of instances to launch (default: 3)")
    parser.add_argument("--delay", type=float, default=1.0, help="Delay between launches in seconds (default: 1.0)")
    
    args = parser.parse_args()
    
    if args.count < 1:
        print("Error: Count must be at least 1")
        sys.exit(1)
    
    if args.count > 10:
        print("Warning: Launching more than 10 instances may impact system performance")
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Cancelled.")
            sys.exit(0)
    
    print(f"Launching {args.count} instances of the publish application...")
    print("Each instance will have multiinstance support enabled.")
    print("You can see instance information in each window.")
    print()
    
    launch_instances(args.count, args.delay)


if __name__ == "__main__":
    main()
