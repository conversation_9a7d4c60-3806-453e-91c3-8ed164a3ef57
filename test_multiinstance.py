#!/usr/bin/env python3
"""
Test script for multiinstance functionality of the publish application.

This script tests the multiinstance support by launching multiple instances
and verifying they can run simultaneously without conflicts.

Usage:
    python test_multiinstance.py [--instances N] [--delay SECONDS]

Author: E3 Automation Team
"""

import os
import sys
import time
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.abspath(__file__))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from apps.publish.utils.instance_manager import InstanceManager


class MultiInstanceTester:
    """Test class for multiinstance functionality."""
    
    def __init__(self, num_instances: int = 3, delay_between_launches: float = 2.0):
        """
        Initialize the multiinstance tester.
        
        Args:
            num_instances: Number of instances to launch
            delay_between_launches: Delay in seconds between launching instances
        """
        self.num_instances = num_instances
        self.delay_between_launches = delay_between_launches
        self.launched_processes: List[subprocess.Popen] = []
        self.logger = logging.getLogger(__name__)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s",
            handlers=[
                logging.FileHandler("multiinstance_test.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def test_instance_manager(self) -> bool:
        """
        Test the instance manager functionality.
        
        Returns:
            True if all tests pass
        """
        self.logger.info("Testing InstanceManager functionality...")
        
        try:
            # Test 1: Create multiple instance managers
            managers = []
            for i in range(3):
                manager = InstanceManager("test_app")
                manager.register_instance(
                    window_title=f"Test Window {i+1}",
                    project_path=f"/test/project/{i+1}"
                )
                managers.append(manager)
                self.logger.info(f"Created instance manager {i+1}: {manager.instance_id[:8]}")
            
            # Test 2: Verify instance detection
            running_instances = managers[0].get_running_instances()
            self.logger.info(f"Detected {len(running_instances)} running instances")
            
            if len(running_instances) != 3:
                self.logger.error(f"Expected 3 instances, found {len(running_instances)}")
                return False
            
            # Test 3: Verify instance numbering
            for i, manager in enumerate(managers):
                instance_number = manager.get_instance_number()
                self.logger.info(f"Manager {i+1} reports instance number: {instance_number}")
            
            # Test 4: Test cleanup
            for manager in managers:
                manager.unregister_instance()
                self.logger.info(f"Unregistered instance: {manager.instance_id[:8]}")
            
            # Verify cleanup
            remaining_instances = managers[0].get_running_instances()
            self.logger.info(f"After cleanup: {len(remaining_instances)} instances remaining")
            
            self.logger.info("InstanceManager tests completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"InstanceManager test failed: {e}")
            return False
    
    def launch_publish_instances(self) -> bool:
        """
        Launch multiple publish application instances.
        
        Returns:
            True if all instances launched successfully
        """
        self.logger.info(f"Launching {self.num_instances} publish application instances...")
        
        # Path to the publish application
        publish_app_path = Path(parent_dir) / "apps" / "publish_3.py"
        
        if not publish_app_path.exists():
            self.logger.error(f"Publish application not found: {publish_app_path}")
            return False
        
        try:
            for i in range(self.num_instances):
                self.logger.info(f"Launching instance {i+1}...")
                
                # Launch with unique instance ID
                instance_id = f"test_instance_{i+1}"
                cmd = [
                    sys.executable,
                    str(publish_app_path),
                    "--instance-id", instance_id
                ]
                
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                self.launched_processes.append(process)
                self.logger.info(f"Launched instance {i+1} with PID: {process.pid}")
                
                # Wait between launches
                if i < self.num_instances - 1:
                    time.sleep(self.delay_between_launches)
            
            self.logger.info(f"All {self.num_instances} instances launched")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to launch instances: {e}")
            return False
    
    def verify_instances_running(self) -> bool:
        """
        Verify that all launched instances are running properly.
        
        Returns:
            True if all instances are running
        """
        self.logger.info("Verifying instances are running...")
        
        # Wait a moment for instances to initialize
        time.sleep(5)
        
        try:
            # Check process status
            running_count = 0
            for i, process in enumerate(self.launched_processes):
                if process.poll() is None:  # Process is still running
                    running_count += 1
                    self.logger.info(f"Instance {i+1} (PID: {process.pid}) is running")
                else:
                    self.logger.warning(f"Instance {i+1} (PID: {process.pid}) has terminated")
                    # Get output for debugging
                    stdout, stderr = process.communicate()
                    if stderr:
                        self.logger.error(f"Instance {i+1} stderr: {stderr}")
            
            # Use instance manager to verify registration
            manager = InstanceManager("publish_app")
            registered_instances = manager.get_running_instances()
            self.logger.info(f"Instance manager reports {len(registered_instances)} registered instances")
            
            success = running_count == self.num_instances
            if success:
                self.logger.info("All instances are running successfully")
            else:
                self.logger.error(f"Only {running_count}/{self.num_instances} instances are running")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to verify instances: {e}")
            return False
    
    def cleanup_instances(self):
        """Clean up all launched instances."""
        self.logger.info("Cleaning up launched instances...")
        
        for i, process in enumerate(self.launched_processes):
            try:
                if process.poll() is None:  # Process is still running
                    self.logger.info(f"Terminating instance {i+1} (PID: {process.pid})")
                    process.terminate()
                    
                    # Wait for graceful termination
                    try:
                        process.wait(timeout=10)
                        self.logger.info(f"Instance {i+1} terminated gracefully")
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"Instance {i+1} did not terminate gracefully, killing...")
                        process.kill()
                        process.wait()
                        self.logger.info(f"Instance {i+1} killed")
                else:
                    self.logger.info(f"Instance {i+1} already terminated")
                    
            except Exception as e:
                self.logger.error(f"Error cleaning up instance {i+1}: {e}")
        
        self.launched_processes.clear()
        self.logger.info("Cleanup completed")
    
    def run_full_test(self) -> bool:
        """
        Run the complete multiinstance test suite.
        
        Returns:
            True if all tests pass
        """
        self.logger.info("Starting multiinstance test suite...")
        
        try:
            # Test 1: Instance Manager functionality
            if not self.test_instance_manager():
                self.logger.error("Instance Manager test failed")
                return False
            
            # Test 2: Launch multiple instances
            if not self.launch_publish_instances():
                self.logger.error("Failed to launch instances")
                return False
            
            # Test 3: Verify instances are running
            if not self.verify_instances_running():
                self.logger.error("Instance verification failed")
                return False
            
            # Wait for user interaction (optional)
            self.logger.info("Instances are running. Press Enter to continue with cleanup...")
            input()
            
            self.logger.info("All multiinstance tests passed!")
            return True
            
        except KeyboardInterrupt:
            self.logger.info("Test interrupted by user")
            return False
        except Exception as e:
            self.logger.error(f"Test suite failed: {e}")
            return False
        finally:
            self.cleanup_instances()


def main():
    """Main entry point for the test script."""
    parser = argparse.ArgumentParser(description="Test multiinstance functionality")
    parser.add_argument("--instances", type=int, default=3, help="Number of instances to launch")
    parser.add_argument("--delay", type=float, default=2.0, help="Delay between launches in seconds")
    parser.add_argument("--manager-only", action="store_true", help="Test only the instance manager")
    
    args = parser.parse_args()
    
    tester = MultiInstanceTester(
        num_instances=args.instances,
        delay_between_launches=args.delay
    )
    
    if args.manager_only:
        # Test only the instance manager
        success = tester.test_instance_manager()
    else:
        # Run full test suite
        success = tester.run_full_test()
    
    if success:
        print("✅ All tests passed!")
        sys.exit(0)
    else:
        print("❌ Tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
