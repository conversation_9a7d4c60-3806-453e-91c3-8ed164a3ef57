"""
Main entry point for the Publish 3 application.

This module provides the minimal main entry point that initializes the service
container and starts the GUI application with multiinstance support.
"""

import logging
import sys
import os
import traceback
import customtkinter as ctk
import uuid
import argparse
from pathlib import Path

# Add the parent directory to the path for imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from apps.publish.container import ServiceContainer
from apps.publish.gui.main_window import MainWindow
from apps.publish.config.logging_config import setup_publish_logging
from apps.publish.utils.instance_manager import InstanceManager


class PublishApplication:
    """Main application class for the Publish 3 application with multiinstance support."""

    def __init__(self, instance_id: str = None):
        """
        Initialize the application.

        Args:
            instance_id: Optional instance ID for multiinstance support
        """
        self.logger = None
        self.container = None
        self.root = None
        self.main_window = None
        self.instance_manager = None
        self.instance_id = instance_id or str(uuid.uuid4())
        self.instance_number = 1
        
    def setup_logging(self):
        """Set up application logging with instance-specific configuration."""
        try:
            # Use the logging configuration from the config module with instance ID
            setup_publish_logging(instance_id=self.instance_id)
            self.logger = logging.getLogger(__name__)
            self.logger.info(f"Logging configured successfully for instance {self.instance_id}")
        except Exception as e:
            # Fallback to basic logging if config fails
            log_filename = f"publish_debug_{self.instance_id[:8]}.log"
            logging.basicConfig(
                level=logging.DEBUG,
                format=f"%(asctime)s [%(levelname)s] [Instance:{self.instance_id[:8]}] %(message)s",
                handlers=[
                    logging.FileHandler(log_filename),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            self.logger = logging.getLogger(__name__)
            self.logger.warning(f"Failed to use advanced logging config, using fallback: {e}")
            
    def setup_theme(self):
        """Set up the application theme."""
        try:
            # Apply the default theme
            ctk.set_appearance_mode("dark")
            
            # Try to load custom theme
            theme_path = os.path.join(parent_dir, "resources", "themes", "red.json")
            if os.path.exists(theme_path):
                ctk.set_default_color_theme(theme_path)
                self.logger.info(f"Applied custom theme: {theme_path}")
            else:
                ctk.set_default_color_theme("red")
                self.logger.info("Applied built-in red theme")
                
        except Exception as e:
            self.logger.warning(f"Failed to apply theme: {e}")
            # Use default theme as fallback
            ctk.set_default_color_theme("blue")
            
    def initialize_instance_manager(self):
        """Initialize the instance manager for multiinstance support."""
        try:
            self.logger.info("Initializing instance manager")
            self.instance_manager = InstanceManager("publish_app")
            self.instance_manager.instance_id = self.instance_id

            # Register this instance
            window_title = f"Publish Project - Instance {self.instance_number}"
            self.instance_manager.register_instance(window_title=window_title)

            # Get instance number for display
            self.instance_number = self.instance_manager.get_instance_number()

            self.logger.info(f"Instance manager initialized - Instance #{self.instance_number}")
        except Exception as e:
            self.logger.error(f"Failed to initialize instance manager: {e}")
            # Don't raise - multiinstance support is optional

    def initialize_container(self):
        """Initialize the service container with instance-specific configuration."""
        try:
            self.logger.info("Initializing service container")
            # Pass instance information to container
            self.container = ServiceContainer(instance_id=self.instance_id)
            self.logger.info("Service container initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize service container: {e}")
            raise
            
    def create_gui(self):
        """Create the main GUI window with instance identification."""
        try:
            self.logger.info("Creating main GUI window")
            self.root = ctk.CTk()

            # Set window title with instance information
            window_title = f"Publish Project - Instance #{self.instance_number}"
            if self.instance_number > 1:
                window_title += f" (ID: {self.instance_id[:8]})"

            self.root.title(window_title)
            self.root.geometry("600x500")

            # Update instance manager with window title
            if self.instance_manager:
                self.instance_manager.update_project_info(window_title=window_title)

            # Create the main window with dependency injection
            self.main_window = MainWindow(self.root, self.container, instance_manager=self.instance_manager)

            self.logger.info("Main GUI window created successfully")

        except Exception as e:
            self.logger.error(f"Failed to create GUI: {e}")
            raise
            
    def run(self):
        """Run the application."""
        try:
            self.logger.info("Starting Publish 3 application")
            
            # Start the GUI main loop
            if self.root:
                self.logger.info("Starting GUI main loop")
                self.root.mainloop()
            else:
                raise RuntimeError("GUI not initialized")
                
        except Exception as e:
            self.logger.error(f"Error in application main loop: {e}")
            raise
        finally:
            self.cleanup()
            
    def cleanup(self):
        """Clean up application resources including instance management."""
        try:
            self.logger.info("Cleaning up application resources")

            # Unregister instance
            if self.instance_manager:
                self.instance_manager.unregister_instance()

            # Dispose container
            if self.container:
                self.container.dispose()

            self.logger.info("Application cleanup completed")

        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
            
    def handle_frozen_environment(self):
        """Handle PyInstaller frozen environment setup."""
        is_frozen = getattr(sys, 'frozen', False)
        
        if is_frozen:
            try:
                # Add the _MEIPASS directory to the PATH environment variable
                os.environ['PATH'] = sys._MEIPASS + os.pathsep + os.environ['PATH']
                
                # Change working directory to the executable directory
                os.chdir(os.path.dirname(sys.executable))
                
                # Set up logging to a file in the user's temp directory
                log_file = os.path.join(os.path.expanduser('~'), 'publish_3.log')
                logging.basicConfig(
                    level=logging.DEBUG,
                    format="%(asctime)s [%(levelname)s] %(message)s",
                    handlers=[
                        logging.FileHandler(log_file),
                        logging.StreamHandler(sys.stdout)
                    ]
                )
                
                if self.logger:
                    self.logger.info(f"Running from frozen environment: {sys.executable}")
                    
            except Exception as e:
                print(f"Error setting up frozen environment: {str(e)}")
                
    def start(self):
        """Start the application with full initialization including multiinstance support."""
        try:
            # Handle frozen environment first
            self.handle_frozen_environment()

            # Set up logging
            self.setup_logging()

            # Initialize instance manager
            self.initialize_instance_manager()

            # Set up theme
            self.setup_theme()

            # Initialize service container
            self.initialize_container()

            # Create GUI
            self.create_gui()

            # Run the application
            self.run()

        except Exception as e:
            if self.logger:
                self.logger.error(f"Fatal error during application startup: {e}")
                self.logger.error(traceback.format_exc())
            else:
                print(f"Fatal error during application startup: {e}")
                print(traceback.format_exc())
            sys.exit(1)


def main():
    """Main entry point function with command line argument support."""
    parser = argparse.ArgumentParser(description="Publish 3 Application with Multiinstance Support")
    parser.add_argument("--instance-id", help="Specify a custom instance ID")
    parser.add_argument("--new-instance", action="store_true", help="Force creation of a new instance")

    args = parser.parse_args()

    # Create application with optional instance ID
    instance_id = args.instance_id if args.instance_id else None
    app = PublishApplication(instance_id=instance_id)
    app.start()


if __name__ == "__main__":
    main()