"""
Manual creation service for integrating with existing ManualCreator functionality.

This module contains the ManualService class that handles manual creation
operations while integrating with the existing manual creation system.
"""

import logging
import os
from typing import Optional, Dict, Any

try:
    # Try to import ManualCreator from lib
    import sys
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    from lib.manual_creator import ManualCreator
    MANUAL_CREATOR_AVAILABLE = True
except ImportError as e:
    logging.warning(f"ManualCreator not available: {e}")
    ManualCreator = None
    MANUAL_CREATOR_AVAILABLE = False


class ManualService:
    """Service for manual creation operations."""
    
    def __init__(self, models_config: Optional[Dict] = None):
        """
        Initialize the manual service.
        
        Args:
            models_config: Dictionary containing model configuration data
        """
        self.models_config = models_config or {}
        self.logger = logging.getLogger(__name__)
        
    def create_manual(self, project_data, output_folder: str) -> 'ManualResult':
        """
        Create a manual for the specified project.
        
        Args:
            project_data: Project data containing manual creation information
            output_folder: Folder where the manual should be created
            
        Returns:
            ManualResult containing success status and details
            
        Raises:
            ManualError: If manual creation fails
        """
        result = ManualResult()
        
        try:
            self.logger.info(f"Starting manual creation for project: {project_data.gss_parent}")
            
            # Check if manual creation is available
            if not self.is_manual_creation_available():
                error_msg = "Manual creation functionality is not available"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "availability_check")
            
            # Validate project data
            validation_result = self._validate_project_data(project_data)
            if not validation_result.is_valid:
                error_msg = f"Project data validation failed: {validation_result.error_message}"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "validation", validation_result.errors)
            
            # Determine category and get model data
            category = self._determine_category(project_data.model)
            if not category:
                error_msg = f"Could not determine category for model: {project_data.model}"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "category_lookup")
            
            model_data = self._get_model_data(category, project_data.model)
            if not model_data:
                error_msg = f"Could not find model data for: {project_data.model}"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "model_data_lookup")
            
            # Create ManualCreator instance
            try:
                manual_creator = ManualCreator(
                    job_folder=output_folder,
                    category=category,
                    model=project_data.model,
                    company=project_data.customer or "",
                    location=project_data.location or "",
                    serial=project_data.serial_number,
                    model_data=model_data,
                    document_number=project_data.gss_parent
                )
                
                self.logger.debug(f"Created ManualCreator instance for model {project_data.model}")
                
            except Exception as e:
                error_msg = f"Failed to create ManualCreator instance: {e}"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "manual_creator_init", e)
            
            # Create the manual
            try:
                manual_creator.create_manual()
                
                # Check if manual files were created
                expected_files = self._get_expected_manual_files(output_folder, project_data.serial_number)
                created_files = [f for f in expected_files if os.path.exists(f)]
                
                if created_files:
                    result.success = True
                    result.created_files = created_files
                    result.output_folder = output_folder
                    self.logger.info(f"Manual creation completed successfully. Created {len(created_files)} files")
                    for file_path in created_files:
                        self.logger.debug(f"Created manual file: {file_path}")
                else:
                    error_msg = "Manual creation completed but no output files were found"
                    self.logger.warning(error_msg)
                    result.success = False
                    result.warnings.append(error_msg)
                
            except Exception as e:
                error_msg = f"Manual creation process failed: {e}"
                self.logger.error(error_msg)
                raise ManualError(error_msg, "manual_creation", e)
            
            return result
            
        except ManualError:
            result.success = False
            raise
        except Exception as e:
            error_msg = f"Unexpected error during manual creation: {e}"
            self.logger.error(error_msg)
            result.success = False
            raise ManualError(error_msg, "unexpected_error", e)
    
    def is_manual_creation_available(self) -> bool:
        """
        Check if manual creation functionality is available.
        
        Returns:
            True if manual creation is available, False otherwise
        """
        # Allow override for testing
        if hasattr(self, '_manual_creator_available_override'):
            return self._manual_creator_available_override
        return MANUAL_CREATOR_AVAILABLE and ManualCreator is not None
    
    def get_supported_models(self) -> Dict[str, list]:
        """
        Get list of models that support manual creation.
        
        Returns:
            Dictionary with categories as keys and lists of supported models as values
        """
        supported = {}
        for category, models in self.models_config.items():
            supported[category] = list(models.keys())
        return supported
    
    def can_create_manual_for_model(self, model: str) -> bool:
        """
        Check if manual creation is supported for the specified model.
        
        Args:
            model: Model name to check
            
        Returns:
            True if manual creation is supported for this model
        """
        if not self.is_manual_creation_available():
            return False
            
        category = self._determine_category(model)
        return category is not None
    
    def _validate_project_data(self, project_data) -> 'ValidationResult':
        """
        Validate project data for manual creation.
        
        Args:
            project_data: Project data to validate
            
        Returns:
            ValidationResult containing validation status and errors
        """
        result = ValidationResult()
        
        # Check required fields
        required_fields = {
            'gss_parent': 'GSS Parent number',
            'serial_number': 'Serial number',
            'model': 'Model'
        }
        
        for field, display_name in required_fields.items():
            if not hasattr(project_data, field) or not getattr(project_data, field):
                result.errors.append(f"{display_name} is required for manual creation")
        
        # Check model exists in configuration
        if hasattr(project_data, 'model') and project_data.model:
            if not self._determine_category(project_data.model):
                result.errors.append(f"Model '{project_data.model}' is not configured for manual creation")
        
        result.is_valid = len(result.errors) == 0
        if not result.is_valid:
            result.error_message = "; ".join(result.errors)
        
        return result
    
    def _determine_category(self, model: str) -> Optional[str]:
        """
        Determine the category for a given model.
        
        Args:
            model: Model name to look up
            
        Returns:
            Category name or None if not found
        """
        for category, models in self.models_config.items():
            if model in models:
                self.logger.debug(f"Found model {model} in category {category}")
                return category
        
        self.logger.debug(f"Model {model} not found in any category")
        return None
    
    def _get_model_data(self, category: str, model: str) -> Optional[tuple]:
        """
        Get model data for manual creation.
        
        Args:
            category: Model category
            model: Model name
            
        Returns:
            Tuple containing (template_path, drawings_path, asme_flag) or None
        """
        try:
            model_info = self.models_config[category][model]
            
            # Handle both dict format (new) and list/tuple format (legacy)
            if isinstance(model_info, dict):
                return (
                    model_info.get('template_path', ''),
                    model_info.get('drawings_path', ''),
                    model_info.get('asme_flag', False)
                )
            elif isinstance(model_info, (list, tuple)) and len(model_info) >= 3:
                return tuple(model_info[:3])  # Take first 3 elements
            else:
                self.logger.error(f"Invalid model data format for {category}/{model}")
                return None
                
        except KeyError as e:
            self.logger.error(f"Model data not found for {category}/{model}: {e}")
            return None
    
    def _get_expected_manual_files(self, output_folder: str, serial_number: str) -> list:
        """
        Get list of expected manual output files.
        
        Args:
            output_folder: Output folder path
            serial_number: Project serial number
            
        Returns:
            List of expected file paths
        """
        expected_files = [
            os.path.join(output_folder, f"{serial_number}_manual.docx"),
            os.path.join(output_folder, f"{serial_number}_manual.pdf"),
            os.path.join(output_folder, "merged", f"{serial_number} Manual.pdf")
        ]
        return expected_files


class ManualResult:
    """Result of manual creation operations."""
    
    def __init__(self):
        """Initialize manual result."""
        self.success = False
        self.created_files = []
        self.output_folder = ""
        self.warnings = []
        self.errors = []
    
    def __str__(self):
        """String representation of manual result."""
        status = "SUCCESS" if self.success else "FAILED"
        return f"ManualResult({status}, {len(self.created_files)} files created)"


class ValidationResult:
    """Result of project data validation."""
    
    def __init__(self):
        """Initialize validation result."""
        self.is_valid = True
        self.errors = []
        self.error_message = ""


class ManualError(Exception):
    """Exception raised when manual creation operations fail."""
    
    def __init__(self, message: str, operation: str = None, original_error: Exception = None, details: Any = None):
        """
        Initialize manual error.
        
        Args:
            message: Error message
            operation: Name of the operation that failed
            original_error: Original exception that caused this error
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.operation = operation
        self.original_error = original_error
        self.details = details
    
    def __str__(self):
        base_msg = self.message
        if self.operation:
            base_msg = f"Manual operation '{self.operation}' failed: {base_msg}"
        if self.original_error:
            base_msg += f" (Original error: {self.original_error})"
        return base_msg