"""
Main application window for the publish application.

This module contains the MainWindow class that handles the primary user interface
and coordinates with the service layer for business operations.
"""

import customtkinter as ctk
from tkinter import filedialog
from typing import Optional, Dict, Any
import logging

from ..models.project_data import ProjectData, TitleBlockData
from ..services.publish_service import PublishService, PublishConfig
from ..services.model_service import ModelService
from ..integrations.e3_client import E3Client
from .widgets import ModelDropdown, SeriesControls
from .dialogs import ErrorDialog, ConfirmationDialog


class MainWindow:
    """Main application window for project publishing."""
    
    def __init__(self, root: ctk.CTk, container):
        """
        Initialize the main window.
        
        Args:
            root: The root CTk window
            container: Service container for dependency injection
        """
        self.root = root
        self.container = container
        self.logger = logging.getLogger(__name__)
        
        # Get services from container
        try:
            self.publish_service = self.container.get_service(PublishService)
            self.model_service = self.container.get_service(ModelService)
            self.e3_client = self.container.get_service(E3Client)
        except Exception as e:
            self.logger.error(f"Failed to get services from container: {e}")
            # Set fallback None values
            self.publish_service = None
            self.model_service = None
            self.e3_client = None
        
        # Form fields - must be defined before setup_ui()
        self.fields = [
            "GSS Parent #",
            "Serial number", 
            "Customer",
            "Location",
            "Title",
            "Sales order #"
        ]
        
        # UI components
        self.entries: Dict[str, ctk.CTkEntry] = {}
        self.model_dropdown: Optional[ModelDropdown] = None
        self.series_controls: Optional[SeriesControls] = None
        self.folder_label: Optional[ctk.CTkLabel] = None
        
        # Data
        self.project_data = ProjectData()
        self.title_block_data = TitleBlockData()
        self.folder_selected = ""
        
        # Initialize the UI
        self.setup_ui()
        
    def create_window(self, title: str = "Publish Project", geometry: str = "600x500") -> ctk.CTk:
        """
        Create and configure the main window.
        
        Args:
            title: Window title
            geometry: Window size
            
        Returns:
            Configured CTk root window
        """
        self.root = ctk.CTk()
        self.root.title(title)
        self.root.geometry(geometry)
        
        self.setup_ui()
        self.load_title_block_data()
        self.populate_fields()
        
        return self.root
        
    def setup_ui(self):
        """Set up the user interface components."""
        if not self.root:
            raise RuntimeError("Root window must be created first")
            
        # Main frame
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create form fields
        self._create_form_fields(main_frame)
        
        # Create model dropdown
        self._create_model_section(main_frame)
        
        # Create publishing options
        self._create_publishing_options(main_frame)
        
        # Create folder selection
        self._create_folder_section(main_frame)
        
        # Create publish button
        self._create_publish_button(main_frame)
        
    def _create_form_fields(self, parent):
        """Create the form input fields."""
        for field in self.fields:
            row_frame = ctk.CTkFrame(parent)
            row_frame.pack(fill="x", padx=5, pady=5)
            
            label = ctk.CTkLabel(row_frame, text=field + ":", width=120)
            label.pack(side="left", padx=5)
            
            entry = ctk.CTkEntry(row_frame, width=200)
            entry.pack(side="right", fill="x", expand=True, padx=5)
            self.entries[field] = entry
            
            # Add event handler for GSS Parent # field
            if field == "GSS Parent #":
                entry.bind('<KeyRelease>', self._on_gss_parent_changed)
                
    def _create_model_section(self, parent):
        """Create the model selection section."""
        model_frame = ctk.CTkFrame(parent)
        model_frame.pack(fill="x", padx=5, pady=5)
        
        model_label = ctk.CTkLabel(model_frame, text="Model:", width=120)
        model_label.pack(side="left", padx=5)
        
        self.model_dropdown = ModelDropdown(
            model_frame,
            values=["Select GSS Parent #"],
            width=200
        )
        self.model_dropdown.pack(side="right", fill="x", expand=True, padx=5)
        
    def _create_publishing_options(self, parent):
        """Create the publishing options section."""
        self.series_controls = SeriesControls(parent)
        self.series_controls.pack(fill="x", padx=5, pady=5)
        
    def _create_folder_section(self, parent):
        """Create the folder selection section."""
        # Browse button frame
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        browse_button = ctk.CTkButton(
            button_frame, 
            text="Browse", 
            command=self.browse_folder
        )
        browse_button.pack(side="left", padx=5)
        
        # Folder display frame
        folder_frame = ctk.CTkFrame(parent)
        folder_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.folder_label = ctk.CTkLabel(
            folder_frame, 
            text="No folder selected", 
            width=200, 
            wraplength=550, 
            justify="left"
        )
        self.folder_label.pack(fill="both", expand=True, padx=10, pady=5)
        
    def _create_publish_button(self, parent):
        """Create the publish button."""
        publish_button = ctk.CTkButton(
            parent, 
            text="Publish", 
            command=self.on_publish_clicked
        )
        publish_button.pack(side="bottom", padx=5, pady=10)
        
    def _on_gss_parent_changed(self, event=None):
        """Handle GSS Parent # field changes."""
        if self.model_service and self.model_dropdown:
            gss_number = self.entries["GSS Parent #"].get()
            models = self.model_service.get_models_for_gss(gss_number)
            self.model_dropdown.update_models(models)
            
    def load_title_block_data(self):
        """Load title block data from E3 project."""
        if not self.e3_client:
            self.logger.warning("E3 client not available - cannot load title block data")
            return
            
        try:
            self.title_block_data = self.e3_client.read_title_block_data()
            self.logger.info("Successfully loaded title block data from E3")
        except Exception as e:
            self.logger.error(f"Failed to load title block data: {e}")
            ErrorDialog(
                self.root,
                "E3 Connection Error",
                f"Failed to load project data from E3:\n{e}"
            ).show()
            
    def populate_fields(self):
        """Populate form fields with title block data."""
        if not self.title_block_data:
            return
            
        # Map title block data to form fields
        field_mapping = {
            "GSS Parent #": self.title_block_data.document_number,
            "Serial number": self.title_block_data.serial_number,
            "Customer": self.title_block_data.customer,
            "Location": self.title_block_data.location,
            "Title": self.title_block_data.description,
            "Sales order #": self.title_block_data.sales_order
        }
        
        # Populate fields
        for field, value in field_mapping.items():
            if field in self.entries and value:
                self.entries[field].delete(0, 'end')
                self.entries[field].insert(0, value)
                
        # Update model dropdown after populating GSS Parent #
        self._on_gss_parent_changed()
        
        # Select matching model if available
        if (self.title_block_data.model and 
            self.model_dropdown and 
            self.title_block_data.model in self.model_dropdown.cget("values")):
            self.model_dropdown.set(self.title_block_data.model)
            
    def browse_folder(self):
        """Open folder selection dialog."""
        folder = filedialog.askdirectory()
        if folder:
            self.folder_selected = folder
            self.folder_label.configure(
                text=f"Selected folder: {folder}", 
                wraplength=550
            )
            self.logger.info(f"Selected output folder: {folder}")
            
    def collect_form_data(self) -> ProjectData:
        """Collect data from form fields."""
        project_data = ProjectData(
            gss_parent=self.entries["GSS Parent #"].get(),
            serial_number=self.entries["Serial number"].get(),
            customer=self.entries["Customer"].get(),
            location=self.entries["Location"].get(),
            title=self.entries["Title"].get(),
            sales_order=self.entries["Sales order #"].get(),
            model=self.model_dropdown.get() if self.model_dropdown else "",
            folder_path=self.folder_selected
        )
        
        return project_data
        
    def create_publish_config(self) -> PublishConfig:
        """Create publish configuration from UI settings."""
        config = PublishConfig()
        
        if self.series_controls:
            config.create_manual = self.series_controls.get_create_manual()
            config.output_base_path = self.folder_selected
            
        return config
        
    def on_publish_clicked(self):
        """Handle publish button click event."""
        try:
            # Collect form data
            project_data = self.collect_form_data()
            
            # Validate data
            validation_result = project_data.validate()
            if not validation_result.is_valid:
                error_msg = "Please fix the following errors:\n\n" + "\n".join(validation_result.errors)
                ErrorDialog(self.root, "Validation Error", error_msg).show()
                return
                
            # Show warnings if any
            if validation_result.has_warnings():
                warning_msg = "Please note the following warnings:\n\n" + "\n".join(validation_result.warnings)
                if not ConfirmationDialog(
                    self.root, 
                    "Validation Warnings", 
                    warning_msg + "\n\nDo you want to continue?"
                ).show():
                    return
                    
            # Create publish configuration
            publish_config = self.create_publish_config()
            
            # Check if series publishing is enabled
            if self.series_controls and self.series_controls.is_series_enabled():
                series_count = self.series_controls.get_series_count()
                self._publish_series(project_data, publish_config, series_count)
            else:
                self._publish_single(project_data, publish_config)
                
        except Exception as e:
            self.logger.error(f"Error during publish operation: {e}")
            ErrorDialog(
                self.root,
                "Publish Error", 
                f"An error occurred during publishing:\n{e}"
            ).show()
            
    def _publish_single(self, project_data: ProjectData, config: PublishConfig):
        """Publish a single project."""
        if not self.publish_service:
            ErrorDialog(
                self.root,
                "Service Error",
                "Publish service is not available"
            ).show()
            return
            
        try:
            self.logger.info(f"Starting single project publish: {project_data.gss_parent} - {project_data.serial_number}")
            
            result = self.publish_service.publish_project(project_data, config)
            
            if result.success:
                self._show_success_message(result)
            else:
                self._show_error_result(result)
                
        except Exception as e:
            self.logger.error(f"Failed to publish project: {e}")
            ErrorDialog(
                self.root,
                "Publish Failed",
                f"Failed to publish project:\n{e}"
            ).show()
            
    def _publish_series(self, project_data: ProjectData, config: PublishConfig, series_count: int):
        """Publish a series of projects."""
        if not self.publish_service:
            ErrorDialog(
                self.root,
                "Service Error", 
                "Publish service is not available"
            ).show()
            return
            
        try:
            self.logger.info(f"Starting series publish: {series_count} projects")
            
            results = self.publish_service.publish_series(project_data, config, series_count)
            
            successful_count = sum(1 for r in results if r.success)
            
            if successful_count == len(results):
                self._show_series_success_message(results)
            else:
                self._show_series_error_results(results)
                
        except Exception as e:
            self.logger.error(f"Failed to publish series: {e}")
            ErrorDialog(
                self.root,
                "Series Publish Failed",
                f"Failed to publish series:\n{e}"
            ).show()
            
    def _show_success_message(self, result):
        """Show success message for single project."""
        message = f"Project published successfully!\n\n"
        message += f"Serial Number: {result.serial_number}\n"
        message += f"Output Path: {result.output_path}\n"
        message += f"Steps Completed: {len(result.steps_completed)}"
        
        if result.warnings:
            message += f"\n\nWarnings:\n" + "\n".join(result.warnings)
            
        # For now, use a simple dialog - could be enhanced with a custom success dialog
        ConfirmationDialog(
            self.root,
            "Publish Complete",
            message
        ).show()
        
    def _show_error_result(self, result):
        """Show error message for failed project."""
        message = f"Project publishing failed!\n\n"
        message += f"Serial Number: {result.serial_number}\n"
        message += f"Errors:\n" + "\n".join(result.errors)
        
        if result.warnings:
            message += f"\n\nWarnings:\n" + "\n".join(result.warnings)
            
        ErrorDialog(
            self.root,
            "Publish Failed",
            message
        ).show()
        
    def _show_series_success_message(self, results):
        """Show success message for series publishing."""
        message = f"Series published successfully!\n\n"
        message += f"Projects: {len(results)}\n"
        message += f"All projects completed without errors."
        
        ConfirmationDialog(
            self.root,
            "Series Publish Complete",
            message
        ).show()
        
    def _show_series_error_results(self, results):
        """Show error message for failed series publishing."""
        successful_count = sum(1 for r in results if r.success)
        failed_count = len(results) - successful_count
        
        message = f"Series publishing completed with errors!\n\n"
        message += f"Total Projects: {len(results)}\n"
        message += f"Successful: {successful_count}\n"
        message += f"Failed: {failed_count}\n\n"
        
        # Show details for failed projects
        failed_results = [r for r in results if not r.success]
        if failed_results:
            message += "Failed Projects:\n"
            for result in failed_results[:5]:  # Show first 5 failures
                message += f"- {result.serial_number}: {result.errors[0] if result.errors else 'Unknown error'}\n"
            if len(failed_results) > 5:
                message += f"... and {len(failed_results) - 5} more"
                
        ErrorDialog(
            self.root,
            "Series Publish Errors",
            message
        ).show()